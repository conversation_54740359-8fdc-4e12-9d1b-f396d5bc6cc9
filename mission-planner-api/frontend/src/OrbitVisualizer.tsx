import React, { useEffect, useRef, useState } from 'react';
import * as THREE from 'three';

interface OrbitParameters {
  semi_major_axis: number;
  eccentricity: number;
  inclination: number;
  longitude_of_ascending_node: number;
  argument_of_periapsis: number;
  true_anomaly: number;
}

interface HohmannTransferResponse {
  first_maneuver: any;
  second_maneuver: any;
  total_delta_v: number;
  transfer_time: number;
  transfer_orbit: OrbitParameters;
}

interface OrbitVisualizerProps {
  initialOrbit: OrbitParameters;
  targetOrbit: OrbitParameters;
  transferResult?: HohmannTransferResponse | null;
}

interface OrbitingBody {
  mesh: THREE.Mesh;
  orbit: OrbitParameters;
  currentAngle: number;
  speed: number;
  trail: THREE.Line;
  trailPoints: THREE.Vector3[];
}

const OrbitVisualizer: React.FC<OrbitVisualizerProps> = ({
  initialOrbit,
  targetOrbit,
  transferResult
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const animationRef = useRef<number>();
  const orbitingBodiesRef = useRef<OrbitingBody[]>([]);
  const transferBodyRef = useRef<OrbitingBody | null>(null);
  const clockRef = useRef<THREE.Clock>(new THREE.Clock());

  const [isAnimating, setIsAnimating] = useState(false);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [showTrails, setShowTrails] = useState(true);

  // Scale factor for visualization (Earth radius = 6371 km)
  const SCALE_FACTOR = 1 / 1000; // 1 unit = 1000 km
  const EARTH_RADIUS = 6.371;

  useEffect(() => {
    if (!mountRef.current) return;

    // Scene setup
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0x000011);
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      mountRef.current.clientWidth / mountRef.current.clientHeight,
      0.1,
      1000
    );
    camera.position.set(100, 50, 100);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Earth
    const earthGeometry = new THREE.SphereGeometry(EARTH_RADIUS, 32, 32);
    const earthMaterial = new THREE.MeshPhongMaterial({
      color: 0x4488ff,
      shininess: 30,
      transparent: true,
      opacity: 0.8
    });
    const earth = new THREE.Mesh(earthGeometry, earthMaterial);
    earth.castShadow = true;
    earth.receiveShadow = true;
    scene.add(earth);

    // Add Earth's atmosphere glow
    const atmosphereGeometry = new THREE.SphereGeometry(EARTH_RADIUS * 1.05, 32, 32);
    const atmosphereMaterial = new THREE.MeshBasicMaterial({
      color: 0x88ccff,
      transparent: true,
      opacity: 0.1,
      side: THREE.BackSide
    });
    const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
    scene.add(atmosphere);

    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);

    const sunLight = new THREE.DirectionalLight(0xffffff, 1.0);
    sunLight.position.set(100, 50, 50);
    sunLight.castShadow = true;
    sunLight.shadow.mapSize.width = 2048;
    sunLight.shadow.mapSize.height = 2048;
    scene.add(sunLight);

    // Add stars background
    const starsGeometry = new THREE.BufferGeometry();
    const starsCount = 1000;
    const starsPositions = new Float32Array(starsCount * 3);

    for (let i = 0; i < starsCount * 3; i++) {
      starsPositions[i] = (Math.random() - 0.5) * 500;
    }

    starsGeometry.setAttribute('position', new THREE.BufferAttribute(starsPositions, 3));
    const starsMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.5 });
    const stars = new THREE.Points(starsGeometry, starsMaterial);
    scene.add(stars);

    // Helper functions
    const createOrbitPath = (orbit: OrbitParameters, color: number, segments = 128) => {
      const points: THREE.Vector3[] = [];
      const a = orbit.semi_major_axis * SCALE_FACTOR;
      const e = orbit.eccentricity;
      const inc = THREE.MathUtils.degToRad(orbit.inclination);
      const lan = THREE.MathUtils.degToRad(orbit.longitude_of_ascending_node);
      const aop = THREE.MathUtils.degToRad(orbit.argument_of_periapsis);

      for (let i = 0; i <= segments; i++) {
        const nu = (i / segments) * 2 * Math.PI; // True anomaly
        const r = a * (1 - e * e) / (1 + e * Math.cos(nu));

        // Position in orbital plane
        const x_orb = r * Math.cos(nu);
        const y_orb = r * Math.sin(nu);
        const z_orb = 0;

        // Rotate to 3D space
        const x = x_orb * (Math.cos(lan) * Math.cos(aop) - Math.sin(lan) * Math.sin(aop) * Math.cos(inc)) -
                  y_orb * (Math.cos(lan) * Math.sin(aop) + Math.sin(lan) * Math.cos(aop) * Math.cos(inc));
        const y = x_orb * (Math.sin(lan) * Math.cos(aop) + Math.cos(lan) * Math.sin(aop) * Math.cos(inc)) -
                  y_orb * (Math.sin(lan) * Math.sin(aop) - Math.cos(lan) * Math.cos(aop) * Math.cos(inc));
        const z = x_orb * Math.sin(aop) * Math.sin(inc) + y_orb * Math.cos(aop) * Math.sin(inc);

        points.push(new THREE.Vector3(x, y, z));
      }

      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const material = new THREE.LineBasicMaterial({ color, transparent: true, opacity: 0.6 });
      return new THREE.Line(geometry, material);
    };

    const getPositionFromOrbit = (orbit: OrbitParameters, trueAnomaly: number) => {
      const a = orbit.semi_major_axis * SCALE_FACTOR;
      const e = orbit.eccentricity;
      const inc = THREE.MathUtils.degToRad(orbit.inclination);
      const lan = THREE.MathUtils.degToRad(orbit.longitude_of_ascending_node);
      const aop = THREE.MathUtils.degToRad(orbit.argument_of_periapsis);
      const nu = trueAnomaly;

      const r = a * (1 - e * e) / (1 + e * Math.cos(nu));

      // Position in orbital plane
      const x_orb = r * Math.cos(nu);
      const y_orb = r * Math.sin(nu);

      // Rotate to 3D space
      const x = x_orb * (Math.cos(lan) * Math.cos(aop) - Math.sin(lan) * Math.sin(aop) * Math.cos(inc)) -
                y_orb * (Math.cos(lan) * Math.sin(aop) + Math.sin(lan) * Math.cos(aop) * Math.cos(inc));
      const y = x_orb * (Math.sin(lan) * Math.cos(aop) + Math.cos(lan) * Math.sin(aop) * Math.cos(inc)) -
                y_orb * (Math.sin(lan) * Math.sin(aop) - Math.cos(lan) * Math.cos(aop) * Math.cos(inc));
      const z = x_orb * Math.sin(aop) * Math.sin(inc) + y_orb * Math.cos(aop) * Math.sin(inc);

      return new THREE.Vector3(x, y, z);
    };

    // Camera controls
    let isDragging = false;
    let previousMousePosition = { x: 0, y: 0 };
    let cameraDistance = 100;
    let cameraTheta = 0;
    let cameraPhi = Math.PI / 4;

    const onMouseDown = (event: MouseEvent) => {
      isDragging = true;
      previousMousePosition = { x: event.clientX, y: event.clientY };
    };

    const onMouseUp = () => {
      isDragging = false;
    };

    const onMouseMove = (event: MouseEvent) => {
      if (!isDragging) return;

      const deltaX = event.clientX - previousMousePosition.x;
      const deltaY = event.clientY - previousMousePosition.y;

      cameraTheta -= deltaX * 0.01;
      cameraPhi = Math.max(0.1, Math.min(Math.PI - 0.1, cameraPhi + deltaY * 0.01));

      updateCameraPosition();

      previousMousePosition = { x: event.clientX, y: event.clientY };
    };

    const onWheel = (event: WheelEvent) => {
      event.preventDefault();
      cameraDistance = Math.max(20, Math.min(300, cameraDistance + event.deltaY * 0.1));
      updateCameraPosition();
    };

    const updateCameraPosition = () => {
      camera.position.x = cameraDistance * Math.sin(cameraPhi) * Math.cos(cameraTheta);
      camera.position.y = cameraDistance * Math.cos(cameraPhi);
      camera.position.z = cameraDistance * Math.sin(cameraPhi) * Math.sin(cameraTheta);
      camera.lookAt(0, 0, 0);
    };

    renderer.domElement.addEventListener('mousedown', onMouseDown);
    renderer.domElement.addEventListener('mouseup', onMouseUp);
    renderer.domElement.addEventListener('mousemove', onMouseMove);
    renderer.domElement.addEventListener('wheel', onWheel);

    // Animation loop
    const animate = () => {
      if (!isAnimating) {
        animationRef.current = requestAnimationFrame(animate);
        renderer.render(scene, camera);
        return;
      }

      const deltaTime = clockRef.current.getDelta() * animationSpeed;

      // Update orbiting bodies
      orbitingBodiesRef.current.forEach((body) => {
        body.currentAngle += body.speed * deltaTime;
        const position = getPositionFromOrbit(body.orbit, body.currentAngle);
        body.mesh.position.copy(position);

        // Update trail
        if (showTrails) {
          body.trailPoints.push(position.clone());
          if (body.trailPoints.length > 100) {
            body.trailPoints.shift();
          }

          const trailGeometry = new THREE.BufferGeometry().setFromPoints(body.trailPoints);
          body.trail.geometry.dispose();
          body.trail.geometry = trailGeometry;
        }
      });

      // Update transfer body if exists
      if (transferBodyRef.current) {
        const transferBody = transferBodyRef.current;
        transferBody.currentAngle += transferBody.speed * deltaTime;
        const position = getPositionFromOrbit(transferBody.orbit, transferBody.currentAngle);
        transferBody.mesh.position.copy(position);

        if (showTrails) {
          transferBody.trailPoints.push(position.clone());
          if (transferBody.trailPoints.length > 50) {
            transferBody.trailPoints.shift();
          }

          const trailGeometry = new THREE.BufferGeometry().setFromPoints(transferBody.trailPoints);
          transferBody.trail.geometry.dispose();
          transferBody.trail.geometry = trailGeometry;
        }
      }

      animationRef.current = requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    // Cleanup
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.dispose();
    };
  }, [isAnimating, animationSpeed, showTrails]);

  // Update orbits when props change
  useEffect(() => {
    if (!sceneRef.current) return;

    // Clear existing orbiting bodies and orbit paths
    orbitingBodiesRef.current.forEach(body => {
      sceneRef.current!.remove(body.mesh);
      sceneRef.current!.remove(body.trail);
    });
    orbitingBodiesRef.current = [];

    if (transferBodyRef.current) {
      sceneRef.current.remove(transferBodyRef.current.mesh);
      sceneRef.current.remove(transferBodyRef.current.trail);
      transferBodyRef.current = null;
    }

    // Clear existing orbit paths
    const orbitsToRemove = sceneRef.current.children.filter(
      child => child.userData.isOrbit
    );
    orbitsToRemove.forEach(orbit => sceneRef.current!.remove(orbit));

    // Create orbit paths
    const initialOrbitPath = createOrbitPath(initialOrbit, 0x00ff00);
    initialOrbitPath.userData = { isOrbit: true, name: 'initial' };
    sceneRef.current.add(initialOrbitPath);

    const targetOrbitPath = createOrbitPath(targetOrbit, 0xff0000);
    targetOrbitPath.userData = { isOrbit: true, name: 'target' };
    sceneRef.current.add(targetOrbitPath);

    // Create orbiting satellites
    const createSatellite = (orbit: OrbitParameters, color: number, size = 0.5) => {
      const geometry = new THREE.SphereGeometry(size, 8, 8);
      const material = new THREE.MeshPhongMaterial({
        color,
        emissive: color,
        emissiveIntensity: 0.3
      });
      const mesh = new THREE.Mesh(geometry, material);
      mesh.castShadow = true;

      // Create trail
      const trailGeometry = new THREE.BufferGeometry();
      const trailMaterial = new THREE.LineBasicMaterial({
        color,
        transparent: true,
        opacity: 0.6
      });
      const trail = new THREE.Line(trailGeometry, trailMaterial);

      return {
        mesh,
        orbit,
        currentAngle: THREE.MathUtils.degToRad(orbit.true_anomaly),
        speed: 0.5, // Radians per second (adjustable)
        trail,
        trailPoints: [] as THREE.Vector3[]
      };
    };

    // Add initial orbit satellite
    const initialSatellite = createSatellite(initialOrbit, 0x00ff00);
    sceneRef.current.add(initialSatellite.mesh);
    sceneRef.current.add(initialSatellite.trail);
    orbitingBodiesRef.current.push(initialSatellite);

    // Add target orbit satellite
    const targetSatellite = createSatellite(targetOrbit, 0xff0000);
    sceneRef.current.add(targetSatellite.mesh);
    sceneRef.current.add(targetSatellite.trail);
    orbitingBodiesRef.current.push(targetSatellite);

    // Add transfer orbit if available
    if (transferResult) {
      const transferOrbitPath = createOrbitPath(transferResult.transfer_orbit, 0xffff00);
      transferOrbitPath.userData = { isOrbit: true, name: 'transfer' };
      sceneRef.current.add(transferOrbitPath);

      // Add transfer satellite
      const transferSatellite = createSatellite(transferResult.transfer_orbit, 0xffff00, 0.3);
      sceneRef.current.add(transferSatellite.mesh);
      sceneRef.current.add(transferSatellite.trail);
      transferBodyRef.current = transferSatellite;
    }

  }, [initialOrbit, targetOrbit, transferResult]);

  return (
    <div className="orbit-visualizer">
      <h3>3D Orbit Visualization</h3>

      {/* Animation Controls */}
      <div className="animation-controls" style={{ marginBottom: '1rem' }}>
        <button
          onClick={() => setIsAnimating(!isAnimating)}
          style={{ marginRight: '0.5rem' }}
        >
          {isAnimating ? '⏸️ Pause' : '▶️ Play'}
        </button>

        <label style={{ marginRight: '1rem' }}>
          Speed:
          <input
            type="range"
            min="0.1"
            max="5"
            step="0.1"
            value={animationSpeed}
            onChange={(e) => setAnimationSpeed(parseFloat(e.target.value))}
            style={{ marginLeft: '0.5rem', width: '80px' }}
          />
          {animationSpeed.toFixed(1)}x
        </label>

        <label>
          <input
            type="checkbox"
            checked={showTrails}
            onChange={(e) => setShowTrails(e.target.checked)}
            style={{ marginRight: '0.5rem' }}
          />
          Show Trails
        </label>
      </div>

      {/* 3D Viewport */}
      <div
        ref={mountRef}
        style={{
          width: '100%',
          height: '500px',
          border: '1px solid #ccc',
          borderRadius: '8px',
          background: '#000',
          cursor: isAnimating ? 'default' : 'grab'
        }}
      />

      {/* Legend and Instructions */}
      <div style={{ marginTop: '1rem' }}>
        <div className="legend" style={{ marginBottom: '0.5rem' }}>
          <div style={{ color: '#00ff00', marginRight: '1rem' }}>🛰️ Initial Orbit</div>
          <div style={{ color: '#ff0000', marginRight: '1rem' }}>🛰️ Target Orbit</div>
          {transferResult && (
            <div style={{ color: '#ffff00', marginRight: '1rem' }}>🚀 Transfer Orbit</div>
          )}
        </div>

        <div className="controls-info" style={{ fontSize: '0.8em', color: '#888' }}>
          <p>🖱️ Drag to rotate • 🖱️ Scroll to zoom • ▶️ Play to see orbital motion</p>
          {transferResult && (
            <p>🚀 The yellow satellite shows the Hohmann transfer trajectory</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrbitVisualizer;
